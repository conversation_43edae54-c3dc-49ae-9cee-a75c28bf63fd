import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Brand Colors
  static const Color primaryGreen = Color(0xFF89B998);
  static const Color secondaryGreen = Color(0xFF4FAD85);
  static const Color accentBlue = Color(0xFFA2E3F6);
  static const Color accentCoral = Color(0xFFDE7A60);
  static const Color accentPink = Color(0xFFFFABC7);

  // Light Theme Colors
  static const Color lightBackground = Color(0xFFFAFBFC);
  static const Color lightSurface = Color(0xFFFFFFFF);
  static const Color lightSurfaceVariant = Color(0xFFF5F7FA);
  static const Color lightOnBackground = Color(0xFF1A1C1E);
  static const Color lightOnSurface = Color(0xFF1A1C1E);
  static const Color lightOutline = Color(0xFFE1E4E8);

  // Dark Theme Colors
  static const Color darkBackground = Color(0xFF0D1117);
  static const Color darkSurface = Color(0xFF161B22);
  static const Color darkSurfaceVariant = Color(0xFF21262D);
  static const Color darkOnBackground = Color(0xFFF0F6FC);
  static const Color darkOnSurface = Color(0xFFF0F6FC);
  static const Color darkOutline = Color(0xFF30363D);

  static ThemeData get lightTheme {
    final colorScheme = ColorScheme.light(
      brightness: Brightness.light,
      primary: primaryGreen,
      onPrimary: Colors.white,
      secondary: secondaryGreen,
      onSecondary: Colors.white,
      tertiary: accentBlue,
      onTertiary: lightOnSurface,
      error: const Color(0xFFDC3545),
      onError: Colors.white,
      surface: lightSurface,
      onSurface: lightOnSurface,
      outline: lightOutline,
      shadow: Colors.black.withValues(alpha: 0.1),
      surfaceContainerHighest: lightSurfaceVariant,
      onSurfaceVariant: lightOnSurface.withValues(alpha: 0.7),
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      visualDensity: VisualDensity.adaptivePlatformDensity,
      splashFactory: InkRipple.splashFactory,

      // Typography with Google Fonts
      textTheme: GoogleFonts.interTextTheme().copyWith(
        displayLarge: GoogleFonts.inter(
          fontSize: 57,
          fontWeight: FontWeight.w400,
          letterSpacing: -0.25,
          color: lightOnSurface,
        ),
        displayMedium: GoogleFonts.inter(
          fontSize: 45,
          fontWeight: FontWeight.w400,
          color: lightOnSurface,
        ),
        displaySmall: GoogleFonts.inter(
          fontSize: 36,
          fontWeight: FontWeight.w400,
          color: lightOnSurface,
        ),
        headlineLarge: GoogleFonts.inter(
          fontSize: 32,
          fontWeight: FontWeight.w600,
          color: lightOnSurface,
        ),
        headlineMedium: GoogleFonts.inter(
          fontSize: 28,
          fontWeight: FontWeight.w600,
          color: lightOnSurface,
        ),
        headlineSmall: GoogleFonts.inter(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: lightOnSurface,
        ),
        titleLarge: GoogleFonts.inter(
          fontSize: 22,
          fontWeight: FontWeight.w500,
          color: lightOnSurface,
        ),
        titleMedium: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.15,
          color: lightOnSurface,
        ),
        titleSmall: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
          color: lightOnSurface,
        ),
        bodyLarge: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.5,
          color: lightOnSurface,
        ),
        bodyMedium: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.25,
          color: lightOnSurface,
        ),
        bodySmall: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.4,
          color: lightOnSurface.withValues(alpha: 0.7),
        ),
        labelLarge: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
          color: lightOnSurface,
        ),
        labelMedium: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
          color: lightOnSurface,
        ),
        labelSmall: GoogleFonts.inter(
          fontSize: 11,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
          color: lightOnSurface.withValues(alpha: 0.7),
        ),
      ),

      // Page transitions
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: ZoomPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          TargetPlatform.fuchsia: ZoomPageTransitionsBuilder(),
        },
      ),

      // AppBar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),

      // Card Theme
      cardTheme: CardThemeData(
        elevation: 4,
        shadowColor: colorScheme.shadow,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        color: lightSurface,
        surfaceTintColor: primaryGreen.withValues(alpha: 0.05),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryGreen,
          foregroundColor: Colors.white,
          elevation: 2,
          shadowColor: colorScheme.shadow,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),

      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryGreen,
          side: BorderSide(color: primaryGreen, width: 1.5),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),

      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryGreen,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: lightSurfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: lightOutline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: lightOutline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: primaryGreen, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFDC3545), width: 2),
        ),
        labelStyle: GoogleFonts.inter(
          color: lightOnSurface.withValues(alpha: 0.7),
          fontSize: 16,
        ),
        hintStyle: GoogleFonts.inter(
          color: lightOnSurface.withValues(alpha: 0.5),
          fontSize: 16,
        ),
      ),

      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryGreen,
        foregroundColor: Colors.white,
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: lightSurface,
        selectedItemColor: primaryGreen,
        unselectedItemColor: lightOnSurface.withValues(alpha: 0.6),
        elevation: 8,
        type: BottomNavigationBarType.fixed,
        selectedLabelStyle: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        unselectedLabelStyle: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
      ),

      // Tab Bar Theme
      tabBarTheme: TabBarThemeData(
        labelColor: primaryGreen,
        unselectedLabelColor: lightOnSurface.withValues(alpha: 0.6),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: primaryGreen, width: 3),
        ),
        labelStyle: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w400,
        ),
      ),

      // Checkbox Theme
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryGreen;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all(Colors.white),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      ),

      // Divider Theme
      dividerTheme: DividerThemeData(
        color: lightOutline,
        thickness: 1,
        space: 1,
      ),
    );
  }

  static ThemeData get darkTheme {
    final colorScheme = ColorScheme.dark(
      brightness: Brightness.dark,
      primary: primaryGreen,
      onPrimary: Colors.black,
      secondary: secondaryGreen,
      onSecondary: Colors.black,
      tertiary: accentBlue,
      onTertiary: darkOnSurface,
      error: const Color(0xFFFF6B6B),
      onError: Colors.black,
      surface: darkSurface,
      onSurface: darkOnSurface,
      outline: darkOutline,
      shadow: Colors.black.withValues(alpha: 0.3),
      surfaceContainerHighest: darkSurfaceVariant,
      onSurfaceVariant: darkOnSurface.withValues(alpha: 0.7),
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      visualDensity: VisualDensity.adaptivePlatformDensity,
      splashFactory: InkRipple.splashFactory,

      // Typography with Google Fonts
      textTheme: GoogleFonts.interTextTheme(ThemeData.dark().textTheme)
          .copyWith(
            displayLarge: GoogleFonts.inter(
              fontSize: 57,
              fontWeight: FontWeight.w400,
              letterSpacing: -0.25,
              color: darkOnSurface,
            ),
            displayMedium: GoogleFonts.inter(
              fontSize: 45,
              fontWeight: FontWeight.w400,
              color: darkOnSurface,
            ),
            displaySmall: GoogleFonts.inter(
              fontSize: 36,
              fontWeight: FontWeight.w400,
              color: darkOnSurface,
            ),
            headlineLarge: GoogleFonts.inter(
              fontSize: 32,
              fontWeight: FontWeight.w600,
              color: darkOnSurface,
            ),
            headlineMedium: GoogleFonts.inter(
              fontSize: 28,
              fontWeight: FontWeight.w600,
              color: darkOnSurface,
            ),
            headlineSmall: GoogleFonts.inter(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: darkOnSurface,
            ),
            titleLarge: GoogleFonts.inter(
              fontSize: 22,
              fontWeight: FontWeight.w500,
              color: darkOnSurface,
            ),
            titleMedium: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.15,
              color: darkOnSurface,
            ),
            titleSmall: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.1,
              color: darkOnSurface,
            ),
            bodyLarge: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              letterSpacing: 0.5,
              color: darkOnSurface,
            ),
            bodyMedium: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              letterSpacing: 0.25,
              color: darkOnSurface,
            ),
            bodySmall: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              letterSpacing: 0.4,
              color: darkOnSurface.withValues(alpha: 0.7),
            ),
            labelLarge: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.1,
              color: darkOnSurface,
            ),
            labelMedium: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.5,
              color: darkOnSurface,
            ),
            labelSmall: GoogleFonts.inter(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.5,
              color: darkOnSurface.withValues(alpha: 0.7),
            ),
          ),

      // Page transitions
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: ZoomPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          TargetPlatform.fuchsia: ZoomPageTransitionsBuilder(),
        },
      ),

      // AppBar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: primaryGreen,
        foregroundColor: Colors.black,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
        iconTheme: const IconThemeData(color: Colors.black),
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),

      // Card Theme
      cardTheme: CardThemeData(
        elevation: 6,
        shadowColor: colorScheme.shadow,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        color: darkSurface,
        surfaceTintColor: primaryGreen.withValues(alpha: 0.1),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryGreen,
          foregroundColor: Colors.black,
          elevation: 3,
          shadowColor: colorScheme.shadow,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),

      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryGreen,
          side: BorderSide(color: primaryGreen, width: 1.5),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),

      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryGreen,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: darkSurfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: darkOutline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: darkOutline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: primaryGreen, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFFF6B6B), width: 2),
        ),
        labelStyle: GoogleFonts.inter(
          color: darkOnSurface.withValues(alpha: 0.7),
          fontSize: 16,
        ),
        hintStyle: GoogleFonts.inter(
          color: darkOnSurface.withValues(alpha: 0.5),
          fontSize: 16,
        ),
      ),

      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryGreen,
        foregroundColor: Colors.black,
        elevation: 8,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: darkSurface,
        selectedItemColor: primaryGreen,
        unselectedItemColor: darkOnSurface.withValues(alpha: 0.6),
        elevation: 8,
        type: BottomNavigationBarType.fixed,
        selectedLabelStyle: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        unselectedLabelStyle: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
      ),

      // Tab Bar Theme
      tabBarTheme: TabBarThemeData(
        labelColor: primaryGreen,
        unselectedLabelColor: darkOnSurface.withValues(alpha: 0.6),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: primaryGreen, width: 3),
        ),
        labelStyle: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w400,
        ),
      ),

      // Checkbox Theme
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryGreen;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all(Colors.black),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      ),

      // Divider Theme
      dividerTheme: DividerThemeData(
        color: darkOutline,
        thickness: 1,
        space: 1,
      ),
    );
  }

  // Legacy color constants for backward compatibility
  static const Color primary =
      accentBlue; // Keep existing primary as accent blue
  static const Color scrim = accentPink;
  static const Color tertiary = accentCoral;
  static const Color secondary = secondaryGreen;
  static const double defaultBorderRadius = 16;
  static const double defaultTextSize = 16;
  static const Color borderColor = Colors.black12;
  static const Color focusedBorderColor = Colors.black45;
  static const double _spacingUnit = 8;
  static const double spacing8 = _spacingUnit / 2;
  static const double spacing7 = _spacingUnit;
  static const double spacing6 = _spacingUnit * 1.5;
  static const double spacing5 = _spacingUnit * 2;
  static const double spacing4 = _spacingUnit * 2.5;
  static const double spacing3 = _spacingUnit * 3;
  static const double spacing2 = _spacingUnit * 3.5;
  static const double spacing1 = _spacingUnit * 4;
  static double lineWidth = 1;
  static const Widget verticalSpacer = SizedBox(height: spacing5);

  // Text styles
  static TextStyle get heading1 => lightTheme.textTheme.headlineLarge!.copyWith(
    fontWeight: FontWeight.bold,
    fontSize: 28,
    color: Colors.black,
  );

  static TextStyle get heading2 => lightTheme.textTheme.headlineMedium!
      .copyWith(fontWeight: FontWeight.bold, fontSize: 24, color: Colors.black);

  static TextStyle get heading3 => lightTheme.textTheme.headlineSmall!.copyWith(
    fontWeight: FontWeight.bold,
    fontSize: 18,
    color: Colors.black,
  );

  static TextStyle get subheading1 => lightTheme.textTheme.bodyLarge!.copyWith(
    fontWeight: FontWeight.normal,
    fontSize: 18,
    color: Colors.black,
  );

  static TextStyle get subheading2 => lightTheme.textTheme.bodyMedium!.copyWith(
    fontWeight: FontWeight.normal,
    fontSize: 14,
    color: Colors.black,
  );

  static TextStyle get paragraph => lightTheme.textTheme.bodySmall!.copyWith(
    fontWeight: FontWeight.normal,
    fontSize: 14,
    color: Colors.black,
  );

  static TextStyle get label => lightTheme.textTheme.labelSmall!.copyWith(
    fontWeight: FontWeight.w600,
    fontSize: 11,
    color: Colors.black,
  );

  static TextStyle get dossierParagraph => GoogleFonts.anonymousPro().copyWith(
    fontWeight: FontWeight.normal,
    fontSize: 14,
    color: Colors.black,
  );

  static TextStyle get dossierSubheading => GoogleFonts.anonymousPro().copyWith(
    fontWeight: FontWeight.normal,
    fontSize: 18,
    color: Colors.black,
  );

  static TextStyle get dossierHeading => GoogleFonts.anonymousPro().copyWith(
    fontWeight: FontWeight.bold,
    fontSize: 28,
    color: Colors.black,
  );

  // Additional theme utilities
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  static Color getAdaptiveColor(
    BuildContext context,
    Color lightColor,
    Color darkColor,
  ) {
    return isDarkMode(context) ? darkColor : lightColor;
  }

  // Gradient definitions for consistent use across the app
  static LinearGradient get primaryGradient => const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryGreen, secondaryGreen],
  );

  static LinearGradient get accentGradient => const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accentBlue, primaryGreen],
  );

  static LinearGradient get surfaceGradient => LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [lightSurface, lightSurface.withValues(alpha: 0.8)],
  );

  static LinearGradient get darkSurfaceGradient => LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [darkSurface, darkSurface.withValues(alpha: 0.8)],
  );

  // Enhanced text styles that adapt to theme
  static TextStyle getAdaptiveHeading1(BuildContext context) {
    return isDarkMode(context)
        ? heading1.copyWith(color: darkOnSurface)
        : heading1;
  }

  static TextStyle getAdaptiveHeading2(BuildContext context) {
    return isDarkMode(context)
        ? heading2.copyWith(color: darkOnSurface)
        : heading2;
  }

  static TextStyle getAdaptiveHeading3(BuildContext context) {
    return isDarkMode(context)
        ? heading3.copyWith(color: darkOnSurface)
        : heading3;
  }

  static TextStyle getAdaptiveSubheading1(BuildContext context) {
    return isDarkMode(context)
        ? subheading1.copyWith(color: darkOnSurface)
        : subheading1;
  }

  static TextStyle getAdaptiveSubheading2(BuildContext context) {
    return isDarkMode(context)
        ? subheading2.copyWith(color: darkOnSurface)
        : subheading2;
  }

  static TextStyle getAdaptiveParagraph(BuildContext context) {
    return isDarkMode(context)
        ? paragraph.copyWith(color: darkOnSurface)
        : paragraph;
  }

  static TextStyle getAdaptiveLabel(BuildContext context) {
    return isDarkMode(context) ? label.copyWith(color: darkOnSurface) : label;
  }
}
