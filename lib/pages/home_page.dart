import 'package:convenient_test/convenient_test.dart';
import 'package:convex_bottom_bar/convex_bottom_bar.dart';
import 'package:diogeneschatbot/bloc/timeline_bloc.dart';
import 'package:diogeneschatbot/constants/usage_constants.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/manage_model_page.dart';
import 'package:diogeneschatbot/models/profile.dart';
import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/pages/chat_rooms_page.dart';
import 'package:diogeneschatbot/pages/contact_list_page.dart';
import 'package:diogeneschatbot/pages/create_post_page.dart';
import 'package:diogeneschatbot/pages/login_signup_page.dart';
import 'package:diogeneschatbot/pages/search_page.dart';
import 'package:diogeneschatbot/pages/timeline_page.dart';
import 'package:diogeneschatbot/pages/usage_history_page.dart';
import 'package:diogeneschatbot/pages/user_profile_page.dart';
// import 'package:diogeneschatbot/pages/atom_feed_page.dart';
import 'package:diogeneschatbot/services/auth_service.dart';
import 'package:diogeneschatbot/services/service_locator.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_drawer.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:diogeneschatbot/widgets/usage_list_widget.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
// import 'package:diogeneschatbot/pages/yolo_page.dart';
// import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:universal_io/io.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum HomePageMark {
  home,
  chat,
  search,
  createPost,
  contactList,
  share,
  timeline,
}

class HomePage extends StatefulWidget {
  const HomePage({super.key, required this.title});

  final String title;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  int _currentIndex = 0;
  String? currentUserId = FirebaseAuth.instance.currentUser?.uid;
  final GlobalKey<ConvexAppBarState> _appBarKey =
      GlobalKey<ConvexAppBarState>();
  TabController? _tabController;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  Widget _buildEnhancedDrawer(
    BuildContext context,
    AuthService authService,
    AppLocalizations localizations,
    Map<String, String> supportedLanguages,
  ) {
    return EnhancedDrawer(
      header: EnhancedDrawerHeader(
        userName: FirebaseAuth.instance.currentUser?.displayName ?? 'User',
        userEmail: FirebaseAuth.instance.currentUser?.email,
        onProfileTap: () {
          Navigator.pop(context);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => UserProfilePage(userId: currentUserId!),
            ),
          );
        },
      ),
      items: [
        DrawerItem(
          title: 'Language',
          icon: Icons.language,
          onTap: () => _showLanguageDialog(context, supportedLanguages),
        ),
        DrawerItem.divider(),
        DrawerItem.header('Navigation'),
        DrawerItem(
          title: 'Usage History',
          icon: Icons.bar_chart,
          onTap: () {
            Navigator.pop(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => UsageHistoryPage(currentUserId!),
              ),
            );
          },
        ),
        DrawerItem(
          title: 'Contacts',
          icon: Icons.contacts,
          onTap: () {
            Navigator.pop(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ContactListScreen(
                  userId: currentUserId!,
                  currentUserId: currentUserId!,
                  showAppBar: true,
                ),
              ),
            );
          },
        ),
        DrawerItem(
          title: 'Search',
          icon: Icons.search,
          onTap: () {
            Navigator.pop(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => SearchPage(currentUserId: currentUserId!),
              ),
            );
          },
        ),
        if (!kIsWeb) ...[
          DrawerItem.divider(),
          DrawerItem.header('Tools'),
          DrawerItem(
            title: 'Local Models',
            icon: Icons.offline_bolt,
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => ModelManagementPage()),
              );
            },
          ),
        ],
        DrawerItem.divider(),
        DrawerItem.header('Support'),
        DrawerItem(
          title: 'Rate Our App',
          icon: Icons.star,
          onTap: () => _rateApp(),
        ),
        DrawerItem(
          title: 'Share App',
          icon: Icons.share,
          onTap: () => _shareApp(),
        ),
        DrawerItem.divider(),
        DrawerItem(
          title: 'Delete Account',
          icon: Icons.delete,
          onTap: () {
            Navigator.pop(context);
            Navigator.of(context).pushNamed('/delete_account');
          },
        ),
        DrawerItem(
          title: 'Sign Out',
          icon: Icons.logout,
          onTap: () => _signOut(authService),
        ),
      ],
    );
  }

  void _showLanguageDialog(
    BuildContext context,
    Map<String, String> supportedLanguages,
  ) {
    Navigator.pop(context); // Close drawer first
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Language'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: supportedLanguages.entries.map((entry) {
                return ListTile(
                  title: Text(entry.value),
                  onTap: () async {
                    changeLanguage(Locale(entry.key));
                    Navigator.pop(context); // Close the dialog
                  },
                );
              }).toList(),
            ),
          ),
        );
      },
    );
  }

  void _rateApp() async {
    final InAppReview inAppReview = InAppReview.instance;
    try {
      if (await inAppReview.isAvailable()) {
        await inAppReview.requestReview();
      } else {
        logger.i("In-app review is not available.");
      }
    } catch (e) {
      logger.i("Error requesting review: $e");
    }
  }

  void _shareApp() {
    String appLink;
    if (Platform.isIOS || Platform.isMacOS) {
      appLink =
          'https://apps.apple.com/us/app/diogenes-ai-chatbot/id6446092554';
    } else if (Platform.isAndroid) {
      appLink =
          'https://play.google.com/store/apps/details?id=com.diogenes.ai.chatbot';
    } else {
      appLink = 'https://diogenesaichatbot.web.app/';
    }
    Share.share('Check out this awesome app: $appLink');
  }

  void _signOut(AuthService authService) async {
    try {
      await authService.signOut();
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => LoginSignupPage()),
        );
      }
    } catch (e) {
      logger.e("Error signing out: $e");
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error signing out: $e')));
      }
    }
  }

  Future<List<String>> _fetchFollowingIds() async {
    ProfileRepository profileRepository = ProfileRepository();
    List<String> followingIds = [];

    try {
      List<Profile> followings = await profileRepository.getFollowings(
        currentUserId!,
      );
      followingIds = followings.map((profile) => profile.id).toList();
    } catch (e) {
      logger.d("Error fetching followings: $e");
    }

    return followingIds;
  }

  void changeLanguage(Locale locale) async {
    final localeNotifier = getIt<ValueNotifier<Locale>>();
    localeNotifier.value = locale; // Update the locale
    // Save the selected locale to SharedPreferences
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('locale', locale.languageCode);
    setState(() {});
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBarStyles.primary(
      title: widget.title,
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => SearchPage(currentUserId: currentUserId!),
              ),
            );
          },
          tooltip: 'Search',
        ),
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () {
            // TODO: Navigate to notifications page
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Notifications coming soon!')),
            );
          },
          tooltip: 'Notifications',
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);

    return ValueListenableBuilder<Locale>(
      valueListenable: getIt<ValueNotifier<Locale>>(),
      builder: (context, locale, child) {
        return Localizations.override(
          context: context,
          locale: locale,
          child: Builder(
            builder: (context) {
              final AppLocalizations localizations = AppLocalizations.of(
                context,
              )!;

              // Create a map for supported languages
              final Map<String, String> supportedLanguages = {
                'en': 'English',
                'zh': '中文',
                'fr': 'Français',
                'de': 'Deutsch',
                'ja': '日本語',
                'el': 'Ελληνικά',
                'it': 'Italiano',
                'es': 'Español',
                'ar': 'العربية', // Arabic
                'hi': 'हिन्दी', // Hindi
                'ru': 'Русский', // Russian
                'tr': 'Türkçe', // Turkish
                'pt': 'Português', // Portuguese
              };

              // Fetch items based on the current locale
              List<Usage> items = UsageConstants.getUsages(localizations);
              return FutureBuilder<List<String>>(
                future: _fetchFollowingIds(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.done) {
                    if (snapshot.hasError) {
                      logger.d("Error fetching followings: ${snapshot.error}");
                    }

                    List<String> followingIds = snapshot.data ?? [];

                    List<Widget> pages = [
                      // Container(),
                      UsageListWidget(
                        currentUserId: currentUserId,
                        items: items,
                      ),
                      if (kIsWeb)
                        BlocProvider(
                          create: (BuildContext context) =>
                              TimelineBloc(followingIds: followingIds)
                                ..add(LoadTimelinePosts()),
                          child: TimelinePage(
                            followingIds: followingIds,
                            currentUserId: currentUserId!,
                          ),
                        ),
                      ChatRoomsPage(currentUserId: currentUserId!),
                      UserProfilePage(userId: currentUserId!),
                    ];

                    _tabController = TabController(
                      length: pages.length,
                      vsync: this,
                    );

                    return Scaffold(
                      appBar: _buildAppBar(),
                      drawer: _buildEnhancedDrawer(
                        context,
                        authService,
                        localizations,
                        supportedLanguages,
                      ),
                      body: pages[_currentIndex],
                      // TabBarView(
                      //     controller: _tabController, children: pages),
                      bottomNavigationBar: ConvexAppBar(
                        initialActiveIndex: _currentIndex,
                        key: _appBarKey,
                        color: Colors.white,
                        activeColor: Colors.white,
                        shadowColor: Colors.black26,
                        backgroundColor: AppTheme.primaryGreen,
                        gradient: AppTheme.primaryGradient,
                        onTap: (index) {
                          setState(() {
                            _currentIndex = index;
                          });
                        },
                        items: [
                          TabItem(
                            icon: Icon(Icons.dashboard_rounded),
                            title: 'Dashboard',
                          ),
                          if (kIsWeb)
                            TabItem(
                              icon: Icon(Icons.timeline_rounded),
                              title: 'Timeline',
                            ),
                          TabItem(
                            icon: Icon(Icons.chat_bubble_rounded),
                            title: 'Chat',
                          ),
                          TabItem(
                            icon: Icon(Icons.person_rounded),
                            title: 'Profile',
                          ),
                        ],
                      ),
                      floatingActionButton: (kIsWeb)
                          ? Mark(
                              name: HomePageMark.createPost,
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: AppTheme.primaryGradient,
                                  borderRadius: BorderRadius.circular(16),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppTheme.primaryGreen.withValues(
                                        alpha: 0.3,
                                      ),
                                      blurRadius: 12,
                                      offset: const Offset(0, 6),
                                    ),
                                  ],
                                ),
                                child: FloatingActionButton(
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => CreatePostPage(
                                          userId: currentUserId!,
                                        ),
                                      ),
                                    );
                                  },
                                  backgroundColor: Colors.transparent,
                                  elevation: 0,
                                  tooltip: 'Create a new post',
                                  child: const Icon(
                                    Icons.add_rounded,
                                    color: Colors.white,
                                    size: 28,
                                  ),
                                ),
                              ),
                            )
                          : null,
                    );
                  } else {
                    return Scaffold(
                      appBar: _buildAppBar(),
                      body: Center(
                        child: LoadingStyles.gradient(
                          message: 'Loading your dashboard...',
                        ),
                      ),
                    );
                  }
                },
              );
            },
          ),
        );
      },
    );
  }
}
