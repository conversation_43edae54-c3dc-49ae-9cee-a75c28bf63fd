import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:diogeneschatbot/models/post.dart';
import 'package:diogeneschatbot/models/profile.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:diogeneschatbot/widgets/search_results_widget.dart';
import 'package:flutter/material.dart';
import 'package:universal_io/io.dart';

class SearchPage extends StatefulWidget {
  final String currentUserId;

  SearchPage({required this.currentUserId});

  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage>
    with SingleTickerProviderStateMixin {
  List<dynamic> _results = [];
  late TextEditingController _searchController;
  late TabController _tabController;
  bool _isLoading = false;
  String _lastSearchQuery = '';

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    int tabLength = (!Platform.isIOS && !Platform.isMacOS) ? 2 : 1;
    _tabController = TabController(vsync: this, length: tabLength);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<List<Profile>> searchProfiles(String query) async {
    // Search for users whose name or username contain the query string
    final usersQuerySnapshot = await FirebaseFirestore.instance
        .collection('users')
        .where('name', isGreaterThanOrEqualTo: query)
        .where('name', isLessThanOrEqualTo: '$query\uf8ff')
        .get();

    final usernamesQuerySnapshot = await FirebaseFirestore.instance
        .collection('users')
        .where('username', isGreaterThanOrEqualTo: query)
        .where('username', isLessThanOrEqualTo: '$query\uf8ff')
        .get();

    // Combine the results of both queries and convert them to Profile objects
    List<Profile> profiles = [
      ...usersQuerySnapshot.docs,
      ...usernamesQuerySnapshot.docs,
    ].map((doc) => Profile.fromFirestore(doc)).toList();

    // Remove duplicates
    profiles = profiles.toSet().toList();

    return profiles;
  }

  Future<List<Post>> searchPosts(String query) async {
    HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'ext-firestore-semantic-search-queryIndex',
    );

    // Run search
    final HttpsCallableResult result = await callable.call(<String, dynamic>{
      'query': [query],
    });

    // Get results
    final Map<String, dynamic> data = Map<String, dynamic>.from(result.data);
    final Map<String, dynamic> dataData = Map<String, dynamic>.from(
      data['data'],
    );
    final List<dynamic> nearestNeighbours = List<dynamic>.from(
      dataData['nearestNeighbors'],
    );
    List<String> paths = nearestNeighbours
        .map(
          (e) => (e['neighbors'] as List<dynamic>)
              .map((f) => f['datapoint']['datapointId'])
              .toList(),
        )
        .expand((element) => element)
        .toList()
        .cast<String>();

    // Fetch documents from Firestore using the ids
    List<Future<DocumentSnapshot<Map<String, dynamic>>>> futures = paths
        .map(
          (id) => FirebaseFirestore.instance.collection('posts').doc(id).get(),
        )
        .toList();

    List<DocumentSnapshot<Map<String, dynamic>>> documents = await Future.wait(
      futures,
    );

    // Filter out documents where isComment is true
    List<DocumentSnapshot<Map<String, dynamic>>> filteredDocuments = documents
        .where((doc) => doc.data()?['isComment'] == false)
        .toList();

    List<Post> posts = filteredDocuments
        .map((doc) => Post.fromFirestore(doc))
        .toList();

    return posts;
  }

  Future<List<dynamic>> _performSearch(String query) async {
    if (query.isNotEmpty) {
      if (_tabController.index == 0) {
        // Perform search for profiles using the search query
        final List<Profile> profiles = await searchProfiles(query);
        return profiles;
      } else {
        // Perform search for posts using the search query
        final List<Post> posts = await searchPosts(query);
        return posts;
      }
    }
    return <dynamic>[];
  }

  Future<void> _onSearchButtonPressed() async {
    final query = _searchController.text.trim();
    if (query.isEmpty) return;

    setState(() {
      _isLoading = true;
      _lastSearchQuery = query;
    });

    try {
      final results = await _performSearch(query);
      if (mounted) {
        setState(() {
          _results = results;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Search failed: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark
          ? AppTheme.darkBackground
          : AppTheme.lightBackground,
      appBar: EnhancedSearchAppBar(
        hintText:
            'Search by name, username${(!Platform.isIOS && !Platform.isMacOS) ? ' or post content' : ''}',
        onSearchChanged: (value) {
          setState(() {});
        },
        onSearchSubmitted: _onSearchButtonPressed,
        actions: [
          if (_searchController.text.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.search, color: Colors.white),
              onPressed: _onSearchButtonPressed,
              tooltip: 'Search',
            ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: isDark ? AppTheme.darkGradient : AppTheme.lightGradient,
        ),
        child: Column(
          children: [
            // Tab bar
            Container(
              color: theme.colorScheme.surface.withValues(alpha: 0.9),
              child: TabBar(
                controller: _tabController,
                labelColor: AppTheme.primaryGreen,
                unselectedLabelColor: theme.colorScheme.onSurface.withValues(
                  alpha: 0.6,
                ),
                indicatorColor: AppTheme.primaryGreen,
                tabs: [
                  Tab(icon: Icon(Icons.people_rounded), text: 'Profiles'),
                  if (!Platform.isIOS && !Platform.isMacOS)
                    Tab(icon: Icon(Icons.article_rounded), text: 'Posts'),
                ],
              ),
            ),
            // Content
            Expanded(
              child: _isLoading
                  ? Center(
                      child: LoadingStyles.gradient(
                        message: 'Searching for "$_lastSearchQuery"...',
                      ),
                    )
                  : _results.isEmpty && _lastSearchQuery.isNotEmpty
                  ? _buildEmptyState()
                  : _lastSearchQuery.isEmpty
                  ? _buildInitialState()
                  : TabBarView(
                      controller: _tabController,
                      children: [
                        // Profiles search results list
                        SearchResults(
                          results: _results,
                          isProfileResults: true,
                          currentUserId: widget.currentUserId,
                        ),
                        // Posts search results list
                        if (!Platform.isIOS && !Platform.isMacOS)
                          SearchResults(
                            results: _results,
                            isProfileResults: false,
                            currentUserId: widget.currentUserId,
                          ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInitialState() {
    final theme = Theme.of(context);
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_rounded,
              size: 80,
              color: AppTheme.primaryGreen.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 24),
            Text(
              'Search for People and Posts',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Enter a name, username${(!Platform.isIOS && !Platform.isMacOS) ? ', or post content' : ''} to start searching',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off_rounded,
              size: 80,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 24),
            Text(
              'No Results Found',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try searching with different keywords or check your spelling',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _results.clear();
                  _lastSearchQuery = '';
                });
              },
              icon: const Icon(Icons.refresh_rounded),
              label: const Text('Clear Search'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
